# Swagger OpenAPI 3.0 集成指南

## 概述

本项目已成功集成 Swagger OpenAPI 3.0，提供完整的 API 文档和在线调试功能。

## 访问方式

### 1. Swagger UI 界面
- **主要访问地址**: `http://localhost:8003/swagger-ui.html`
- **便捷访问地址**: `http://localhost:8003/api/doc`
- **兼容访问地址**: `http://localhost:8003/api/docs`

### 2. API 文档 JSON
- **OpenAPI 3.0 JSON**: `http://localhost:8003/v3/api-docs`
- **便捷获取**: `http://localhost:8003/api/json`

### 3. API 信息
- **基本信息**: `http://localhost:8003/api/info`

## 功能特性

### 1. API 分组
- **预测分析**: `/forecasts/**` - 预测分析相关接口
- **标签配置**: `/labelConfig/**` - 标签配置管理接口
- **标签审视**: `/labelAudit/**` - 标签审视相关接口
- **分组分析**: `/groupAnalysts/**` - 分组分析相关接口
- **用户中心**: `/userCenter/**` - 用户中心相关接口
- **LTS任务集成**: `/lts/**` - LTS任务集成接口
- **所有接口**: `/**` - 查看所有接口

### 2. 安全认证
- **Bearer Token**: JWT 认证支持
- **API Key**: 头部 X-API-KEY 认证支持

### 3. 多环境支持
- **本地开发**: `http://localhost:8003`
- **开发环境**: `https://api-dev.huawei.com`
- **测试环境**: `https://api-test.huawei.com`
- **生产环境**: `https://api.huawei.com`

## 配置说明

### 1. 依赖配置
已在 `pom.xml` 中添加以下依赖：
```xml
<!-- Swagger OpenAPI 3.0 依赖 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
    <version>1.7.0</version>
</dependency>
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-webmvc-core</artifactId>
    <version>1.7.0</version>
</dependency>
```

### 2. 应用配置
在 `application.properties` 中配置：
```properties
# ========== Swagger OpenAPI 配置 ==========
# 启用Swagger UI
springdoc.swagger-ui.enabled=true
# Swagger UI访问路径
springdoc.swagger-ui.path=/swagger-ui.html
# API文档路径
springdoc.api-docs.path=/v3/api-docs
# 启用API文档
springdoc.api-docs.enabled=true
```

### 3. Java 配置
- **配置类**: `com.huawei.it.fcst.profits.config.OpenApiConfig`
- **访问控制器**: `com.huawei.it.fcst.profits.controller.ApiDocController`

## 注解说明

### 1. 类级别注解
```java
@Tag(name = "接口分组名", description = "接口分组描述")
```

### 2. 方法级别注解
```java
@Operation(summary = "接口摘要", description = "接口详细描述")
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "成功"),
    @ApiResponse(responseCode = "400", description = "请求参数错误"),
    @ApiResponse(responseCode = "500", description = "服务器内部错误")
})
```

### 3. 参数注解
```java
@Parameter(description = "参数描述", required = true, 
          schema = @Schema(implementation = RequestClass.class))
```

## 启动验证

### 1. 启动应用
```bash
cd opt.fcst.profits.parent
mvn clean package -Dmaven.test.skip=true
java -jar opt.fcst.profits.start/target/opt.fcst.profits.start-1.0-SNAPSHOT.jar
```

### 2. 验证访问
1. 打开浏览器访问: `http://localhost:8003/swagger-ui.html`
2. 查看 API 分组是否正确显示
3. 测试接口调用功能
4. 验证参数和响应文档是否完整

## 注意事项

1. **端口配置**: 默认端口为 8003，可在配置文件中修改
2. **认证配置**: 生产环境需要配置适当的安全认证
3. **环境切换**: 根据部署环境修改服务器地址配置
4. **接口权限**: 部分接口可能需要特定权限才能访问

## 故障排除

### 1. 无法访问 Swagger UI
- 检查应用是否正常启动
- 确认端口号是否正确
- 检查防火墙设置

### 2. API 文档不完整
- 检查 Controller 注解是否正确
- 确认包扫描路径是否包含所有 Controller
- 查看应用日志是否有错误信息

### 3. 接口调用失败
- 检查请求参数格式
- 确认认证信息是否正确
- 查看服务端日志排查问题

## 扩展功能

### 1. 自定义主题
可以通过配置修改 Swagger UI 的外观主题

### 2. 接口测试
支持直接在 Swagger UI 中测试接口调用

### 3. 文档导出
支持导出 OpenAPI 3.0 格式的 JSON/YAML 文档

## 集成完成状态

✅ **编译验证**: 项目编译成功，所有依赖正确解析
✅ **依赖配置**: 已添加 springdoc-openapi 和兼容性依赖
✅ **配置文件**: application.properties 已配置 Swagger 相关设置
✅ **注解完善**: 主要 Controller 接口已添加 OpenAPI 3.0 注解
✅ **访问控制器**: 已创建便捷的 API 文档访问入口
✅ **兼容性处理**: 保留了原有的 @Api 和 @ApiOperation 注解

## 已完成的工作

### 1. 依赖管理
- 添加了 `springdoc-openapi-ui` 1.7.0
- 添加了 `springdoc-openapi-webmvc-core` 1.7.0
- 添加了 `swagger-annotations` 1.6.14 用于兼容旧注解

### 2. 配置类创建
- **OpenApiConfig**: 完整的 OpenAPI 3.0 配置
- **ApiDocController**: 便捷的文档访问控制器

### 3. 接口注解完善
已为以下 Controller 添加了 OpenAPI 3.0 注解：
- **IForecastsController**: 预测分析接口
- **IGroupAnalystsController**: 分组分析接口
- **ILabelConfigController**: 标签配置接口
- **ILabelAuditController**: 标签审视接口
- **IUserCenterController**: 用户中心接口
- **ILtsTaskIntegrationController**: LTS任务集成接口

### 4. API 分组配置
- 预测分析: `/forecasts/**`
- 标签配置: `/labelConfig/**`
- 标签审视: `/labelAudit/**`
- 分组分析: `/groupAnalysts/**`
- 用户中心: `/userCenter/**`
- LTS任务集成: `/lts/**`
- 所有接口: `/**`

## 技术特点

- **双注解支持**: 同时支持 OpenAPI 3.0 和传统 Swagger 2.x 注解
- **完整分组**: 按业务模块进行 API 分组管理
- **安全认证**: 支持 JWT Bearer Token 和 API Key 认证
- **多环境**: 支持开发、测试、生产环境配置
- **便捷访问**: 提供多种访问路径和重定向功能

---

**集成完成时间**: 2024-06-17
**版本**: v1.0.0
**维护团队**: 华为IT团队

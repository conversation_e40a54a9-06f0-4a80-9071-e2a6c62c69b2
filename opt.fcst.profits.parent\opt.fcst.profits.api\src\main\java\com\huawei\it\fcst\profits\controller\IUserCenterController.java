/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.BulletinInfoVO;
import com.huawei.it.fcst.profits.vo.request.FopRecordRequest;
import com.huawei.it.fcst.profits.vo.request.LabelModifyRequest;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import java.util.Map;

/**
 * IUserCenterController
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Path("/userCenter")
@Consumes( {"application/json"})
@Produces( {"application/json"})
public interface IUserCenterController {
    /**
     * getModifyDataByPage
     *
     * @param labelRequest labelRequest
     * @return CommonResult
     */
    @POST
    @Path("/getModifyDataByPage")
    CommonResult getModifyDataByPage(LabelModifyRequest labelRequest);

    /**
     * findFopRecordByPage
     *
     * @param vo vo
     * @return CommonResult
     */
    @POST
    @Path("/findFopRecordByPage")
    CommonResult findFopRecordByPage(FopRecordRequest vo);

    /**
     * downLoadFileByKey
     *
     * @param response response
     * @param map key
     * @return CommonResult
     */
    @POST
    @Path("/downLoadFileByKey")
    CommonResult downLoadFileByKey(@Context HttpServletResponse response, Map<String,String> map) throws CommonApplicationException;



    /**
     * selectBg
     *
     * @param pageVO pageVO
     * @param bgCode bgCode
     * @param bgName bgName
     * @return PagedResult
     */
    @GET
    @Path("/selectBg/page/{pageSize}/{curPage}")
    PagedResult selectBg(@PathParam("") PageVO pageVO, @QueryParam("bgCode") String bgCode,
        @QueryParam("bgName") String bgName);

    /**
     * selectLv1
     *
     * @param pageVO pageVO
     * @param lv1Code lv1Code
     * @param lv1Name lv1Name
     * @return PagedResult
     */
    @GET
    @Path("/selectLv1/page/{pageSize}/{curPage}")
    PagedResult selectLv1(@PathParam("") PageVO pageVO, @QueryParam("lv1Code") String lv1Code,
        @QueryParam("lv1Name") String lv1Name);

    /**
     * selectLv2
     *
     * @param pageVO pageVO
     * @param lv2Code lv2Code
     * @param lv2Name lv2Name
     * @return PagedResult
     */
    @GET
    @Path("/selectLv2/page/{pageSize}/{curPage}")
    PagedResult selectLv2(@PathParam("") PageVO pageVO, @QueryParam("lv2Code") String lv2Code,
        @QueryParam("lv2Name") String lv2Name);

    /**
     * 获取当前用户公告相关信息
     *
     * @param vo
     * @return
     * @throws CommonApplicationException
     */
    @POST
    @Path("/bulletinInfo/query")
    CommonResult getStatusByUserId(BulletinInfoVO vo);

    /**
     * 保存当前用户的公告阅读状态
     *
     * @param vo
     * @return
     */
    @POST
    @Path("/bulletinInfo/save")
    CommonResult saveStatus(BulletinInfoVO vo);
}
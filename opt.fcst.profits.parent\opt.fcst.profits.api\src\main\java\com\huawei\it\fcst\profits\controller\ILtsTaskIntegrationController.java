/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.CellFuncParamVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.io.IOException;

/**
 * 功能描述lts对接
 *
 * <AUTHOR>
 * @since 2023年06月15日
 */
@Path("/lts")
@Consumes( {"application/json"})
@Produces( {"application/json"})
public interface ILtsTaskIntegrationController {
    @POST
    @Path("/triggerTask")
    CommonResult triggerTask(CellFuncParamVO funcParamVO) ;
    @POST
    @Path("/asynTriggerTask")
    CommonResult asynTriggerTask(CellFuncParamVO funcParamVO) ;
    @GET
    @Path("/taskStart")
    CommonResult taskStart() throws ApplicationException, IOException;

    @GET
    @Path("/getTaskStatus")
    CommonResult getTaskStatus() throws ApplicationException, IOException;

    @GET
    @Path("/getAddressByIP")
    CommonResult getAddressByIP() throws ApplicationException, IOException;
}

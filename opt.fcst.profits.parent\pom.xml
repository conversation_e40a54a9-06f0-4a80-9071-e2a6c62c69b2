<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.huawei.it.fcst</groupId>
	<artifactId>opt.fcst.profits.parent</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<modules>
		<module>opt.fcst.profits.api</module>
		<module>opt.fcst.profits.impl</module>
		<module>opt.fcst.profits.start</module>
	</modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<jdk.version>1.8</jdk.version>
		<spring.version>5.3.39-h2</spring.version>
		<lombok.version>1.18.26</lombok.version>
		<hutool.version>5.8.25</hutool.version>
		<spring-boot.version>2.7.18</spring-boot.version>
		<httpclient5.version>5.2.3</httpclient5.version>
		<jackson.version>2.16.2</jackson.version>
		<jackson-databind.version>2.16.2</jackson-databind.version>
		<jackson.annotations.version>2.16.2</jackson.annotations.version>
		<jackson.jaxrs-providers.version>2.16.2</jackson.jaxrs-providers.version>
		<jackson.jaxrs-base.version>2.16.2</jackson.jaxrs-base.version>
		<tomcat.version>9.0.104</tomcat.version>
		<cxf.version>3.5.10</cxf.version>
		<poi.version>5.4.0</poi.version>
	</properties>

	<parent>
		<groupId>com.huawei.his.framework</groupId>
		<artifactId>jalor-dependencies</artifactId>
		<version>6.6.8.0.RELEASE</version>
	</parent>

	<dependencies>

		<!--升级6.6.6 认证升级 -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>huawei-security-dynamicsalt</artifactId>
			<version>2.6.18-RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>transmittable-thread-local</artifactId>
			<version>2.13.2</version>
		</dependency>
		<!-- API管控 Jar -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-webservice-support</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>woodstox-core</artifactId>
					<groupId>com.fasterxml.woodstox</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.jxls</groupId>
			<artifactId>jxls</artifactId>
			<version>2.13.0</version>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- jalor-store-s3配置 -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-store-s3</artifactId>
			<version>${jalor.version}</version>
		</dependency>
		<dependency>
			<artifactId>esdk-obs-java-bundle</artifactId>
			<groupId>com.huaweicloud</groupId>
			<version>3.23.9.1</version>
		</dependency>
		<!-- jalor-store-s3配置 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.25</version>
		</dependency>
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-starter-biz</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>jalor-inspection</artifactId>
					<groupId>com.huawei.his.framework</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-->230905 修改开源依赖<-->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-starter-excel-biz</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>mysql-connector-j</artifactId>
					<groupId>com.mysql</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jalor-download</artifactId>
					<groupId>com.huawei.his.framework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>edm3-client-sdk</artifactId>
					<groupId>com.huawei.it.edm</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jalor-excel-impl</artifactId>
					<groupId>com.huawei.his.framework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-excel-impl</artifactId>
			<version>6.6.8.0-SP2.RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>mysql-connector-j</artifactId>
					<groupId>com.mysql</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huawei.it.edm</groupId>
			<artifactId>edm3-client-sdk</artifactId>
			<version>3.2.3.6</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>bcprov-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 工具jar -->
		<!-- 引入gauss驱动 Jar -->
		<dependency>
			<groupId>com.huaweicloud.dws</groupId>
			<artifactId>huaweicloud-dws-jdbc</artifactId>
			<version>8.5.0.1-200</version>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.7.2</version>
		</dependency>
		<!-- 引入gauss驱动 Jar -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<!-- 版本级单元测试配置 -->
		<dependency>
			<groupId>com.huawei.dt</groupId>
			<artifactId>dt4j-starter-boot</artifactId>
			<version>1.2.6</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>asm</artifactId>
					<groupId>org.ow2.asm</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 单元测试 Jar -->
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>objenesis</artifactId>
					<groupId>org.objenesis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-rule-agent</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-core</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>objenesis</artifactId>
					<groupId>org.objenesis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.huawei.it.jalor5</groupId>
			<artifactId>jalor5.xauth.api</artifactId>
			<version>1.2</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 多维度处理补丁 -->
		<dependency>
			<groupId>com.huawei.it.jalor5</groupId>
			<artifactId>jalor5.reverseauth</artifactId>
			<version>1.0.1-RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 多维度处理补丁 -->
		<!-- 角色切换问题修改 -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-dispersed-cacheclean-biz</artifactId>
			<version>${jalor.version}</version>
		</dependency>
		<!-- API管控 Jar -->
		<!-- 加解密 -->
		<dependency>
			<groupId>com.huawei.kmssdk</groupId>
			<artifactId>kmssdk</artifactId>
			<version>3.0.1.4</version>
		</dependency>
		<!-- end加解密 -->

		<!-- 脱敏 -->
		<dependency>
			<groupId>com.huawei.it.jalor5</groupId>
			<artifactId>jalor5.log.desensitization</artifactId>
			<version>1.9.3</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huawei.wisecloud.secure</groupId>
			<artifactId>LogFilter</artifactId>
			<version>1.2.0.306</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 脱敏 -->
		<!-- https l2 送检整改 -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-https</artifactId>
			<version>${jalor.version}</version>
		</dependency>
		<!-- https l2 送检整改 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.3</version>
			<exclusions>
				<exclusion>
					<artifactId>poi-ooxml-schemas</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.thoughtworks.xstream</groupId>
				<artifactId>xstream</artifactId>
				<version>1.4.21</version>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>3.5.13</version>
				<scope>compile</scope>
			</dependency>
			<dependency>
				<artifactId>authentication-client</artifactId>
				<groupId>com.huawei.it.commonservice</groupId>
				<version>2.5.0.7</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-i18n</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-security-api</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-web-support</artifactId>
				<version>6.6.8.0-SP3.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-upload</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-api</artifactId>
				<version>6.6.8.0-SP6.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-rpc-stub</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-auth-jwt</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-authorization</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-orm</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-config-configcenter</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-web</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-boot</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-audit-aop</artifactId>
				<version>${jalor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-core</artifactId>
				<version>6.6.8.0-SP9.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-login</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-aegis</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-usf-client</artifactId>
				<version>6.6.8.0-SP4.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-sgov-huawei</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-rpc-vega</artifactId>
				<version>6.6.8.0-SP2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-helper</artifactId>
				<version>6.6.8.0-SP3.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-jwt</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-loadbalancer</artifactId>
				<version>6.6.8.0-SP6.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okio</groupId>
				<artifactId>okio</artifactId>
				<version>3.5.0</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jettison</groupId>
				<artifactId>jettison</artifactId>
				<version>1.5.4</version>
			</dependency>
			<dependency>
				<groupId>org.freemarker</groupId>
				<artifactId>freemarker</artifactId>
				<version>2.3.32</version>
			</dependency>
			<dependency>
				<groupId>org.yaml</groupId>
				<artifactId>snakeyaml</artifactId>
				<version>2.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-core</artifactId>
				<version>${tomcat.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-el</artifactId>
				<version>${tomcat.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-websocket</artifactId>
				<version>${tomcat.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.tomcat</groupId>
				<artifactId>tomcat-servlet-api</artifactId>
				<version>${tomcat.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.tomcat</groupId>
				<artifactId>tomcat-coyote</artifactId>
				<version>${tomcat.version}</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.12.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-compress</artifactId>
				<version>1.26.1</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.16.1</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-spring-boot-starter-jaxws</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-spring-boot-starter-jaxrs</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-rt-frontend-jaxrs</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-core</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>1.16.1</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>33.0.0-jre</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk18on</artifactId>
				<version>1.78.1</version>
			</dependency>
			<dependency>
				<groupId>com.mikesamuel</groupId>
				<artifactId>json-sanitizer</artifactId>
				<version>1.2.3</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-excelant</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml-lite</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-scratchpad</artifactId>
				<version>${poi.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>

		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.22.2</version>
					<configuration>
						<skipTests>true</skipTests>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>

		<!-- 版本级单元测试配置 -->


		<plugins><!-- 不要放在pluginManagement下 -->
			<plugin>
				<groupId>com.huawei.dt</groupId>
				<artifactId>dt4j-coverage-maven-plugin</artifactId>
				<version>1.2.6</version>
				<configuration>
					<reportName>TestReport</reportName>
					<reportVersion>1.0.0</reportVersion>
					<reportUser>TestUser</reportUser>
					<checkoutRelativeDirectory>opt.fcst.profits.parent</checkoutRelativeDirectory>
					<includeStats>
						<item>com/huawei/it/fcst/profits/vo/*</item>
						<item>com/huawei/it/fcst/profits/common/utils/biz/*</item>
						<item>com/huawei/it/fcst/profits/service/config/impl/*</item>
						<item>com/huawei/it/fcst/profits/service/usercenter/impl/*</item>
					</includeStats>
				</configuration>
				<executions>
					<!-- 必须添加instrument任务，否则无法采集到class的指令执行数据 -->
					<execution>
						<id>instrument</id>
						<goals>
							<goal>instrument</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!-- G.MAVEN.08 发布构建阶段构建前使用dependency-lock-maven-plugin插件锁定版本号，并将dependencies-lock.json归档到源代码仓库。 -->
			<plugin>
				<groupId>se.vandmo</groupId>
				<artifactId>dependency-lock-maven-plugin</artifactId>
				<version>0.13</version>
				<configuration>
					<filename>dependencies-lock.json</filename>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<showWarnings>true</showWarnings>
					<compilerArgs>
						<arg>-Xlint:all</arg>
					</compilerArgs>
				</configuration>
			</plugin>
		</plugins>
		<!-- 版本级单元测试配置 -->
	</build>
</project>
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;

/**
 * IForecastsController
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Path("/forecasts")
@Consumes({"application/json"})
@Produces({"application/json"})
@Validated
@Tag(name = "预测分析", description = "预测分析相关接口，包括预算版本、产品线、BG信息、指标筛选等功能")
public interface IForecastsController {
    /**
     * [获取预算版本信息列表]
     *
     * @param forecastsRequest forecastsRequest
     * @return List<CommonVO> 版本信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getBudgetVersionInfo")
    @Operation(summary = "获取预算版本信息列表", description = "根据查询条件获取预算版本信息列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = CommonResult.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    CommonResult getBudgetVersionInfo(
        @Parameter(description = "预测请求参数", required = true, schema = @Schema(implementation = ForecastsRequest.class))
        ForecastsRequest forecastsRequest);

    /**
     * [获取产品线信息列表]
     *
     * @return List<CommonVO> 产品线信息
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getProductLine")
    @Operation(summary = "获取产品线信息列表", description = "根据查询条件获取产品线信息列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    CommonResult getProductLine(
        @Parameter(description = "预测请求参数", required = true) @Valid ForecastsRequest forecastsRequest);

    @POST
    @Path("/getBgInfo")
    @Operation(summary = "获取BG信息", description = "获取业务组信息列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    CommonResult getBgInfo(
        @Parameter(description = "预测请求参数", required = true) ForecastsRequest forecastsRequest);

    @POST
    @Path("/getSopInfo")
    @Operation(summary = "获取SOP信息", description = "获取标准作业程序信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    CommonResult getSopInfo(
        @Parameter(description = "预测请求参数", required = true) @Valid ForecastsRequest forecastsRequest);

    @POST
    @Path("/getRolePermission")
    @Operation(summary = "获取角色权限", description = "获取当前用户的角色权限信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "权限不足"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    CommonResult getRolePermission(
        @Parameter(description = "预测请求参数", required = true) ForecastsRequest forecastsRequest);


    /**
     * [获取指标筛选列表]
     *
     * @param l1InfoVO L1名称
     * @return List<CommonVO> 指标筛选信息
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @GET
    @Path("/getIndicatorsInfo")
    CommonResult getIndicatorsInfo(ForecastsRequest l1InfoVO);

    /**
     * [获取设备收入及制毛率信息]
     *
     * @param equipVO 入参
     * @return PagedResult<FcstForecastsEquipVO>  设备收入及制毛率信息
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getEquipmentGrossInfo")
    CommonResult getEquipmentGrossInfo(ForecastsRequest equipVO);

    /**
     * [导出设备收入及制毛率信息]
     *
     * @param response         response
     * @param forecastsRequest forecastsRequest
     * @return CommonResult  设备收入及制毛率信息
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportEquipmentGrossInfo")
    CommonResult exportEquipmentGrossInfo(@Context HttpServletResponse response, ForecastsRequest forecastsRequest)
            throws ApplicationException;

    /**
     * [获取结构和制毛率信息]
     *
     * @param l1InfoVO 入参
     * @return PagedResult<FcstForecastsL1InfoVO> 结构和制毛率信息
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getStructureGrossInfo")
    CommonResult getStructureGrossInfo(ForecastsRequest l1InfoVO);

    /**
     * [导出结构和制毛率信息]
     *
     * @param response         response
     * @param forecastsRequest forecastsRequest
     * @return CommonResult
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportStructureGrossInfo")
    CommonResult exportStructureGrossInfo(@Context HttpServletResponse response, ForecastsRequest forecastsRequest)
            throws ApplicationException;

    /**
     * [导出结构和制毛率信息]
     *
     * @param response         response
     * @param forecastsRequest forecastsRequest
     * @return CommonResult
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/exportFactorByLv1")
    CommonResult exportFactorByLv1(@Context HttpServletResponse response, ForecastsRequest forecastsRequest)
            throws ApplicationException;


    /**
     * [获取L2业务信息]
     *
     * @param l2InfoVO 入参
     * @return void
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getL2GrapInfo")
    CommonResult getL2GrapInfo(ForecastsRequest l2InfoVO);

    /**
     * [获取L1业务转结率信息、获取L1平均价格信息]
     *
     * @param l1InfoVO 入参
     * @return void
     * @throws ApplicationException
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getL1PolylineInfo")
    CommonResult getL1PolylineInfo(ForecastsRequest l1InfoVO);

    /**
     * 获取L1对价等转换系数
     *
     * @param l1InfoVO 入参
     * @return CommonResult
     */
    @POST
    @Path("/getL1McaAdjustRatio")
    CommonResult getL1McaAdjustRatio(ForecastsRequest l1InfoVO);

    /**
     * 获取L1量价到损益制毛调整率
     *
     * @param l1InfoVO 入参
     * @return CommonResult
     */
    @POST
    @Path("/getL1MgpAdjustRatio")
    CommonResult getL1MgpAdjustRatio(ForecastsRequest l1InfoVO);

    /**
     * [获取L2平均价格信息top5]
     *
     * @param l1InfoVO 入参
     * @return List<FcstForecastsL1InfoVO> L1平均价格信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getL2PolylineInfo")
    CommonResult getL2PolylineInfo(ForecastsRequest l1InfoVO);

    /**
     * 获取标签更新后最新的日期数据
     *
     * @param forecastsRequest 入参
     * @return List<FcstForecastsL1InfoVO> L1平均价格信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getUpdateDataTimeInfo")
    CommonResult getUpdateDataTimeInfo(ForecastsRequest forecastsRequest);

    /**
     * 获取指定产业可展开lv1的详情
     *
     * @param forecastsRequest 入参
     * @return List<FcstForecastsL1InfoVO> L1平均价格信息
     * <AUTHOR>
     * @since 2022年10月10日
     */
    @POST
    @Path("/getSelectedProductLv1Detail")
    CommonResult getSelectedProductLv1Detail(ForecastsRequest forecastsRequest);

    @Path("/getForecastsStepMethod")
    @GET
    CommonResult getForecastsStepMethod(@QueryParam("") ForecastsRequest forecastsRequest) throws ApplicationException;

}

package com.huawei.it.fcst.profits.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * OpenAPI 3.0 配置类
 * 用于配置Swagger文档的基本信息、安全认证、API分组等
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Configuration
public class OpenApiConfig {

    /**
     * 配置OpenAPI基本信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("预测利润系统 API 文档")
                        .description("华为IT预测利润系统的RESTful API接口文档，提供完整的接口说明和在线调试功能")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("华为IT团队")
                                .email("<EMAIL>")
                                .url("https://www.huawei.com"))
                        .license(new License()
                                .name("华为内部使用")
                                .url("https://www.huawei.com")))
                .servers(Arrays.asList(
                        new Server().url("http://localhost:8003").description("本地开发环境"),
                        new Server().url("https://api-dev.huawei.com").description("开发环境"),
                        new Server().url("https://api-test.huawei.com").description("测试环境"),
                        new Server().url("https://api.huawei.com").description("生产环境")))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT认证token"))
                        .addSecuritySchemes("apiKey", new SecurityScheme()
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .name("X-API-KEY")
                                .description("API密钥认证")))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .addSecurityItem(new SecurityRequirement().addList("apiKey"));
    }

    /**
     * 预测分析相关API分组
     */
    @Bean
    public GroupedOpenApi forecastsApi() {
        return GroupedOpenApi.builder()
                .group("预测分析")
                .pathsToMatch("/forecasts/**")
                .build();
    }

    /**
     * 标签配置相关API分组
     */
    @Bean
    public GroupedOpenApi labelConfigApi() {
        return GroupedOpenApi.builder()
                .group("标签配置")
                .pathsToMatch("/labelConfig/**")
                .build();
    }

    /**
     * 标签审视相关API分组
     */
    @Bean
    public GroupedOpenApi labelAuditApi() {
        return GroupedOpenApi.builder()
                .group("标签审视")
                .pathsToMatch("/labelAudit/**")
                .build();
    }

    /**
     * 分组分析相关API分组
     */
    @Bean
    public GroupedOpenApi groupAnalystsApi() {
        return GroupedOpenApi.builder()
                .group("分组分析")
                .pathsToMatch("/groupAnalysts/**")
                .build();
    }

    /**
     * 用户中心相关API分组
     */
    @Bean
    public GroupedOpenApi userCenterApi() {
        return GroupedOpenApi.builder()
                .group("用户中心")
                .pathsToMatch("/userCenter/**")
                .build();
    }

    /**
     * LTS任务集成相关API分组
     */
    @Bean
    public GroupedOpenApi ltsTaskApi() {
        return GroupedOpenApi.builder()
                .group("LTS任务集成")
                .pathsToMatch("/lts/**")
                .build();
    }

    /**
     * 所有API分组
     */
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
                .group("所有接口")
                .pathsToMatch("/**")
                .build();
    }
}
